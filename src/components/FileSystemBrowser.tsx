import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { FileSystemItem } from '../types';
import { SSHService } from '../services/SSHService';
import { getFileIcon, formatFileSize } from '../utils/fileUtils';

interface FileSystemBrowserProps {
  sshService: SSHService;
  onFileSelect: (file: FileSystemItem) => void;
  onDirectoryChange: (path: string) => void;
  currentPath: string;
}

export const FileSystemBrowser: React.FC<FileSystemBrowserProps> = ({
  sshService,
  onFileSelect,
  onDirectoryChange,
  currentPath,
}) => {
  const [items, setItems] = useState<FileSystemItem[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadDirectory(currentPath);
  }, [currentPath]);

  const loadDirectory = async (path: string) => {
    if (!sshService.isConnected()) {
      setError('SSH连接未建立');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const directoryItems = await sshService.listDirectory(path);
      setItems(directoryItems);
    } catch (err) {
      setError(`加载目录失败: ${err}`);
      Alert.alert('错误', `无法加载目录: ${err}`);
    } finally {
      setLoading(false);
    }
  };

  const handleItemPress = (item: FileSystemItem) => {
    if (item.type === 'directory') {
      onDirectoryChange(item.path);
    } else {
      onFileSelect(item);
    }
  };

  const navigateUp = () => {
    const parentPath = currentPath.split('/').slice(0, -1).join('/') || '/';
    onDirectoryChange(parentPath);
  };

  const getItemIcon = (item: FileSystemItem) => {
    return getFileIcon(item.name, item.type === 'directory');
  };

  const renderItem = ({ item }: { item: FileSystemItem }) => (
    <TouchableOpacity
      className="bg-dark-surface mx-3 my-0.5 rounded-lg border border-dark-border"
      onPress={() => handleItemPress(item)}
    >
      <View className="flex-row items-center p-3">
        <Text className="text-2xl mr-3">{getItemIcon(item)}</Text>
        <View className="flex-1">
          <Text className="text-base font-medium text-dark-text mb-1">{item.name}</Text>
          <View className="flex-row justify-between">
            <Text className="text-xs text-dark-text-secondary">{formatFileSize(item.size)}</Text>
            <Text className="text-xs text-dark-text-muted font-mono">{item.permissions}</Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center bg-dark-bg">
        <ActivityIndicator size="large" color="#4ade80" />
        <Text className="mt-3 text-base text-dark-text-secondary">加载中...</Text>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-dark-bg">
      {/* 路径导航 */}
      <View className="bg-dark-surface p-3 border-b border-dark-border">
        <TouchableOpacity
          className="bg-dark-accent border border-dark-accent px-3 py-1.5 rounded-md self-start mb-2"
          onPress={navigateUp}
        >
          <Text className="text-dark-bg text-sm font-medium">⬆️ 上级目录</Text>
        </TouchableOpacity>
        <Text className="text-sm text-dark-text-secondary font-mono">{currentPath}</Text>
      </View>

      {/* 错误提示 */}
      {error && (
        <View className="bg-red-900/20 border border-red-500/30 p-3 m-3 rounded-md">
          <Text className="text-red-400 text-sm">{error}</Text>
        </View>
      )}

      {/* 文件列表 */}
      <FlatList
        data={items}
        renderItem={renderItem}
        keyExtractor={(item) => item.path}
        className="flex-1"
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};


