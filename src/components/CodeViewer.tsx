import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  Dimensions,
} from 'react-native';
import { CodeSelection, FileSystemItem } from '../types';
import { SSHService } from '../services/SSHService';

interface CodeViewerProps {
  sshService: SSHService;
  file: FileSystemItem;
  onCodeSelect: (selection: CodeSelection) => void;
  onClose: () => void;
}

export const CodeViewer: React.FC<CodeViewerProps> = ({
  sshService,
  file,
  onCodeSelect,
  onClose,
}) => {
  const [content, setContent] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selection, setSelection] = useState<CodeSelection | null>(null);
  const [isSelecting, setIsSelecting] = useState(false);
  const scrollViewRef = useRef<ScrollView>(null);

  useEffect(() => {
    loadFileContent();
  }, [file.path]); // 只依赖文件路径，避免函数依赖

  const loadFileContent = async () => {
    setLoading(true);
    setError(null);

    try {
      const fileContent = await sshService.readFile(file.path);
      setContent(fileContent);
    } catch (err) {
      setError(`加载文件失败: ${err}`);
      Alert.alert('错误', `无法加载文件: ${err}`);
    } finally {
      setLoading(false);
    }
  };



  const renderLineNumbers = () => {
    const lines = content.split('\n');
    return (
      <View className="bg-gray-800 px-2 py-2 border-r border-gray-600">
        {lines.map((_, index) => (
          <Text
            key={index}
            className="text-gray-500 text-xs font-mono text-right min-w-10"
            style={{ lineHeight: 20 }}
          >
            {index + 1}
          </Text>
        ))}
      </View>
    );
  };

  const renderCodeContent = () => {
    const lines = content.split('\n');

    return (
      <View className="flex-1 px-3 py-2">
        {lines.map((line, index) => (
          <TouchableOpacity
            key={index}
            className={`min-h-5 py-0.5 ${
              selection &&
              index >= selection.startLine - 1 &&
              index <= selection.endLine - 1
                ? 'bg-blue-800'
                : ''
            }`}
            onPress={() => handleLinePress(index + 1)}
            onLongPress={() => startSelection(index + 1)}
          >
            <Text
              className="text-gray-200 text-xs font-mono"
              style={{ lineHeight: 20 }}
            >
              {line || ' '}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  const handleLinePress = (lineNumber: number) => {
    if (isSelecting && selection) {
      // 扩展选择
      const newSelection = {
        ...selection,
        endLine: lineNumber,
        selectedText: getSelectedText(selection.startLine, lineNumber),
      };
      setSelection(newSelection);
    } else {
      // 开始新选择
      startSelection(lineNumber);
    }
  };

  const startSelection = (lineNumber: number) => {
    const newSelection: CodeSelection = {
      startLine: lineNumber,
      endLine: lineNumber,
      startColumn: 0,
      endColumn: 0,
      selectedText: getSelectedText(lineNumber, lineNumber),
    };
    setSelection(newSelection);
    setIsSelecting(true);
  };

  const getSelectedText = (startLine: number, endLine: number): string => {
    const lines = content.split('\n');
    const start = Math.min(startLine, endLine) - 1;
    const end = Math.max(startLine, endLine) - 1;
    return lines.slice(start, end + 1).join('\n');
  };

  const confirmSelection = () => {
    if (selection) {
      onCodeSelect(selection);
      Alert.alert('代码已选择', '选中的代码将作为上下文发送给AI助手');
    }
    setIsSelecting(false);
  };

  const cancelSelection = () => {
    setSelection(null);
    setIsSelecting(false);
  };

  if (loading) {
    return (
      <View className="flex-1 justify-center items-center bg-gray-900">
        <ActivityIndicator size="large" color="#007AFF" />
        <Text className="mt-3 text-base text-gray-300">加载文件中...</Text>
      </View>
    );
  }

  if (error) {
    return (
      <View className="flex-1 justify-center items-center bg-gray-900 p-5">
        <Text className="text-red-400 text-base text-center mb-5">{error}</Text>
        <TouchableOpacity
          className="bg-blue-500 px-5 py-2.5 rounded-md"
          onPress={loadFileContent}
        >
          <Text className="text-white text-base font-medium">重试</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View className="flex-1 bg-gray-900">
      {/* 头部工具栏 */}
      <View className="flex-row items-center bg-gray-800 px-4 py-3 border-b border-gray-600">
        <TouchableOpacity className="p-2 mr-3" onPress={onClose}>
          <Text className="text-white text-lg font-bold">✕</Text>
        </TouchableOpacity>
        <Text className="flex-1 text-white text-base font-medium">{file.name}</Text>
        <View className="flex-row">
          {isSelecting && (
            <>
              <TouchableOpacity
                className="bg-blue-500 px-3 py-1.5 rounded mr-2"
                onPress={confirmSelection}
              >
                <Text className="text-white text-sm font-medium">确认</Text>
              </TouchableOpacity>
              <TouchableOpacity
                className="bg-blue-500 px-3 py-1.5 rounded"
                onPress={cancelSelection}
              >
                <Text className="text-white text-sm font-medium">取消</Text>
              </TouchableOpacity>
            </>
          )}
        </View>
      </View>

      {/* 选择提示 */}
      {isSelecting && (
        <View className="bg-gray-700 p-2 border-b border-gray-600">
          <Text className="text-gray-300 text-xs text-center">
            点击行号选择代码，长按开始选择，再次点击扩展选择范围
          </Text>
        </View>
      )}

      {/* 代码内容 */}
      <ScrollView
        ref={scrollViewRef}
        className="flex-1"
        horizontal={true}
        showsHorizontalScrollIndicator={true}
        showsVerticalScrollIndicator={true}
      >
        <View
          className="flex-row"
          style={{ minWidth: Dimensions.get('window').width }}
        >
          {renderLineNumbers()}
          {renderCodeContent()}
        </View>
      </ScrollView>

      {/* 选择信息 */}
      {selection && (
        <View className="bg-gray-800 p-2 border-t border-gray-600">
          <Text className="text-gray-300 text-xs text-center">
            已选择: 第{selection.startLine}行 - 第{selection.endLine}行
            ({selection.endLine - selection.startLine + 1}行)
          </Text>
        </View>
      )}
    </View>
  );
};


