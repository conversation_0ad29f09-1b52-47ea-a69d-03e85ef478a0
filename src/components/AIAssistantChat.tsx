import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { AIMessage, AIAssistantStatus, CodeSelection } from '../types';
import { AIAgent } from '../types';

interface AIAssistantChatProps {
  agent: AIAgent;
  codeContext?: CodeSelection;
  filePath?: string;
  onClearContext: () => void;
}

export const AIAssistantChat: React.FC<AIAssistantChatProps> = ({
  agent,
  codeContext,
  filePath,
  onClearContext,
}) => {
  const [messages, setMessages] = useState<AIMessage[]>([]);
  const [inputText, setInputText] = useState('');
  const [status, setStatus] = useState<AIAssistantStatus>(AIAssistantStatus.IDLE);
  const [isTyping, setIsTyping] = useState(false);
  const flatListRef = useRef<FlatList>(null);

  useEffect(() => {
    // 监听Agent状态变化
    agent.onStatusChange((newStatus) => {
      setStatus(newStatus);
      if (newStatus === AIAssistantStatus.PROCESSING) {
        setIsTyping(true);
      } else {
        setIsTyping(false);
      }
    });

    // 监听Agent消息
    agent.onMessage((message) => {
      setMessages(prev => [...prev, message]);
      // 自动滚动到底部
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    });

    // 获取初始状态
    setStatus(agent.getStatus());
  }, [agent]);

  const sendMessage = async () => {
    if (!inputText.trim() || status !== AIAssistantStatus.READY) {
      return;
    }

    const messageText = inputText.trim();
    setInputText('');

    try {
      await agent.sendMessage(messageText, codeContext, filePath);
    } catch (error) {
      Alert.alert('错误', `发送消息失败: ${error}`);
    }
  };

  const getStatusText = () => {
    switch (status) {
      case AIAssistantStatus.IDLE:
        return '未启动';
      case AIAssistantStatus.STARTING:
        return '启动中...';
      case AIAssistantStatus.READY:
        return '就绪';
      case AIAssistantStatus.PROCESSING:
        return '处理中...';
      case AIAssistantStatus.ERROR:
        return '错误';
      default:
        return '未知状态';
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case AIAssistantStatus.READY:
        return '#4CAF50';
      case AIAssistantStatus.PROCESSING:
        return '#FF9800';
      case AIAssistantStatus.ERROR:
        return '#F44336';
      default:
        return '#9E9E9E';
    }
  };

  const renderMessage = ({ item }: { item: AIMessage }) => (
    <View
      className={`my-1 ${
        item.type === 'user' ? 'items-end' : 'items-start'
      }`}
    >
      <View
        className={`max-w-4/5 p-3 rounded-2xl border ${
          item.type === 'user'
            ? 'bg-dark-accent border-dark-accent'
            : 'bg-dark-surface border-dark-border'
        }`}
      >
        {/* 代码上下文显示 */}
        {item.codeContext && item.filePath && (
          <View className="mb-2 p-2 bg-dark-bg/50 rounded-md">
            <Text className="text-xs text-dark-text-secondary mb-1 font-medium">
              📄 {item.filePath} (第{item.codeContext.startLine}-{item.codeContext.endLine}行)
            </Text>
            <View className="bg-dark-bg p-2 rounded border-l-4 border-dark-accent">
              <Text className="text-xs font-mono text-dark-text">
                {item.codeContext.selectedText}
              </Text>
            </View>
          </View>
        )}

        <Text
          className={`text-base leading-6 ${
            item.type === 'user' ? 'text-dark-bg' : 'text-dark-text'
          }`}
        >
          {item.content}
        </Text>

        <Text
          className={`text-xs mt-1 ${
            item.type === 'user'
              ? 'text-dark-bg/70'
              : 'text-dark-text-muted'
          }`}
        >
          {item.timestamp.toLocaleTimeString()}
        </Text>
      </View>
    </View>
  );

  const renderTypingIndicator = () => {
    if (!isTyping) return null;

    return (
      <View className="my-1 items-start">
        <View className="max-w-4/5 p-3 rounded-2xl bg-dark-surface border border-dark-border">
          <View className="flex-row items-center">
            <ActivityIndicator size="small" color="#4ade80" />
            <Text className="ml-2 text-sm text-dark-text-secondary italic">AI助手正在思考...</Text>
          </View>
        </View>
      </View>
    );
  };

  return (
    <KeyboardAvoidingView
      className="flex-1 bg-dark-bg"
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      {/* 状态栏 */}
      <View className="bg-dark-surface p-3 border-b border-dark-border">
        <View className="flex-row items-center mb-2">
          <View
            className="w-2 h-2 rounded-full mr-2 border border-dark-border"
            style={{ backgroundColor: getStatusColor() }}
          />
          <Text className="text-sm text-dark-text font-medium">{getStatusText()}</Text>
        </View>

        {/* 代码上下文显示 */}
        {codeContext && filePath && (
          <View className="flex-row items-center bg-dark-accent/20 border border-dark-accent/30 p-2 rounded-lg">
            <Text className="flex-1 text-xs text-dark-accent">
              📄 {filePath.split('/').pop()} (第{codeContext.startLine}-{codeContext.endLine}行)
            </Text>
            <TouchableOpacity className="p-1 ml-2" onPress={onClearContext}>
              <Text className="text-dark-accent text-base font-bold">✕</Text>
            </TouchableOpacity>
          </View>
        )}
      </View>

      {/* 消息列表 */}
      <FlatList
        ref={flatListRef}
        data={messages}
        renderItem={renderMessage}
        keyExtractor={(item) => item.id}
        className="flex-1 px-3"
        showsVerticalScrollIndicator={false}
        ListFooterComponent={renderTypingIndicator}
      />

      {/* 输入区域 */}
      <View className="flex-row items-end bg-dark-surface px-3 py-2 border-t border-dark-border">
        <TextInput
          className="flex-1 border border-dark-border bg-dark-input rounded-2xl px-4 py-2 max-h-25 text-base text-dark-text"
          value={inputText}
          onChangeText={setInputText}
          placeholder="输入消息..."
          placeholderTextColor="#707070"
          multiline
          maxLength={1000}
          editable={status === AIAssistantStatus.READY}
        />
        <TouchableOpacity
          className={`px-4 py-2.5 rounded-2xl ml-2 border ${
            status !== AIAssistantStatus.READY || !inputText.trim()
              ? 'bg-dark-button border-dark-border'
              : 'bg-dark-accent border-dark-accent'
          }`}
          onPress={sendMessage}
          disabled={status !== AIAssistantStatus.READY || !inputText.trim()}
        >
          <Text className={`text-base font-medium ${
            status !== AIAssistantStatus.READY || !inputText.trim()
              ? 'text-dark-text-muted'
              : 'text-dark-bg'
          }`}>发送</Text>
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
};


