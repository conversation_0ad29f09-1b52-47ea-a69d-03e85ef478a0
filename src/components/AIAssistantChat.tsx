import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  FlatList,
  Alert,
  KeyboardAvoidingView,
  Platform,
  ActivityIndicator,
} from 'react-native';
import { Box } from '@/components/ui/box';
import { VStack } from '@/components/ui/vstack';
import { HStack } from '@/components/ui/hstack';
import { Button, ButtonText } from '@/components/ui/button';
import { Input, InputField } from '@/components/ui/input';
import { Text as UIText } from '@/components/ui/text';
import { Pressable } from '@/components/ui/pressable';
import { AIMessage, AIAssistantStatus, CodeSelection } from '../types';
import { AIAgent } from '../types';

interface AIAssistantChatProps {
  agent: AIAgent;
  codeContext?: CodeSelection;
  filePath?: string;
  onClearContext: () => void;
}

export const AIAssistantChat: React.FC<AIAssistantChatProps> = ({
  agent,
  codeContext,
  filePath,
  onClearContext,
}) => {
  const [messages, setMessages] = useState<AIMessage[]>([]);
  const [inputText, setInputText] = useState('');
  const [status, setStatus] = useState<AIAssistantStatus>(AIAssistantStatus.IDLE);
  const [isTyping, setIsTyping] = useState(false);
  const flatListRef = useRef<FlatList>(null);

  useEffect(() => {
    // 监听Agent状态变化
    agent.onStatusChange((newStatus) => {
      setStatus(newStatus);
      if (newStatus === AIAssistantStatus.PROCESSING) {
        setIsTyping(true);
      } else {
        setIsTyping(false);
      }
    });

    // 监听Agent消息
    agent.onMessage((message) => {
      setMessages(prev => [...prev, message]);
      // 自动滚动到底部
      setTimeout(() => {
        flatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    });

    // 获取初始状态
    setStatus(agent.getStatus());
  }, [agent]);

  const sendMessage = async () => {
    if (!inputText.trim() || status !== AIAssistantStatus.READY) {
      return;
    }

    const messageText = inputText.trim();
    setInputText('');

    try {
      await agent.sendMessage(messageText, codeContext, filePath);
    } catch (error) {
      Alert.alert('错误', `发送消息失败: ${error}`);
    }
  };

  const getStatusText = () => {
    switch (status) {
      case AIAssistantStatus.IDLE:
        return '未启动';
      case AIAssistantStatus.STARTING:
        return '启动中...';
      case AIAssistantStatus.READY:
        return '就绪';
      case AIAssistantStatus.PROCESSING:
        return '处理中...';
      case AIAssistantStatus.ERROR:
        return '错误';
      default:
        return '未知状态';
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case AIAssistantStatus.READY:
        return '#4CAF50';
      case AIAssistantStatus.PROCESSING:
        return '#FF9800';
      case AIAssistantStatus.ERROR:
        return '#F44336';
      default:
        return '#9E9E9E';
    }
  };

  const renderMessage = ({ item }: { item: AIMessage }) => (
    <Box
      className={`my-1 ${
        item.type === 'user' ? 'items-end' : 'items-start'
      }`}
    >
      <Box
        className={`max-w-4/5 p-3 rounded-2xl border ${
          item.type === 'user'
            ? 'bg-primary-500 border-primary-500'
            : 'bg-background-50 border-outline-200'
        }`}
      >
        {/* 代码上下文显示 */}
        {item.codeContext && item.filePath && (
          <Box className="mb-2 p-2 bg-background-100 rounded-md">
            <UIText className="text-xs text-typography-600 mb-1 font-medium">
              {item.filePath} (第{item.codeContext.startLine}-{item.codeContext.endLine}行)
            </UIText>
            <Box className="bg-background-0 p-2 rounded border-l-4 border-primary-500">
              <UIText className="text-xs font-mono text-typography-900">
                {item.codeContext.selectedText}
              </UIText>
            </Box>
          </Box>
        )}

        <UIText
          className={`text-base leading-6 ${
            item.type === 'user' ? 'text-typography-0' : 'text-typography-900'
          }`}
        >
          {item.content}
        </UIText>

        <UIText
          className={`text-xs mt-1 ${
            item.type === 'user'
              ? 'text-typography-200'
              : 'text-typography-500'
          }`}
        >
          {item.timestamp.toLocaleTimeString()}
        </UIText>
      </Box>
    </Box>
  );

  const renderTypingIndicator = () => {
    if (!isTyping) return null;

    return (
      <View className="my-1 items-start">
        <View className="max-w-4/5 p-3 rounded-2xl bg-dark-surface border border-dark-border">
          <View className="flex-row items-center">
            <ActivityIndicator size="small" color="#4ade80" />
            <Text className="ml-2 text-sm text-dark-text-secondary italic">AI助手正在思考...</Text>
          </View>
        </View>
      </View>
    );
  };

  return (
    <KeyboardAvoidingView
      className="flex-1 bg-background-0"
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      {/* 状态栏 */}
      <VStack className="bg-background-50 p-3 border-b border-outline-200">
        <HStack className="items-center mb-2">
          <Box
            className="w-2 h-2 rounded-full mr-2 border border-outline-300"
            style={{ backgroundColor: getStatusColor() }}
          />
          <UIText className="text-sm text-typography-900 font-medium">{getStatusText()}</UIText>
        </HStack>

        {/* 代码上下文显示 */}
        {codeContext && filePath && (
          <HStack className="items-center bg-primary-100 border border-primary-200 p-2 rounded-lg">
            <UIText className="flex-1 text-xs text-primary-700">
              {filePath.split('/').pop()} (第{codeContext.startLine}-{codeContext.endLine}行)
            </UIText>
            <Pressable className="p-1 ml-2" onPress={onClearContext}>
              <UIText className="text-primary-700 text-base font-bold">×</UIText>
            </Pressable>
          </HStack>
        )}
      </VStack>

      {/* 消息列表 */}
      <FlatList
        ref={flatListRef}
        data={messages}
        renderItem={renderMessage}
        keyExtractor={(item) => item.id}
        className="flex-1 px-3"
        showsVerticalScrollIndicator={false}
        ListFooterComponent={renderTypingIndicator}
      />

      {/* 输入区域 */}
      <HStack className="items-end bg-background-50 px-3 py-2 border-t border-outline-200">
        <TextInput
          className="flex-1 border border-outline-300 bg-background-0 rounded-2xl px-4 py-2 max-h-25 text-base text-typography-900"
          value={inputText}
          onChangeText={setInputText}
          placeholder="输入消息..."
          placeholderTextColor="#9CA3AF"
          multiline
          maxLength={1000}
          editable={status === AIAssistantStatus.READY}
        />
        <Button
          className={`px-4 py-2.5 rounded-2xl ml-2 ${
            status !== AIAssistantStatus.READY || !inputText.trim()
              ? 'bg-background-200'
              : 'bg-primary-500'
          }`}
          onPress={sendMessage}
          disabled={status !== AIAssistantStatus.READY || !inputText.trim()}
        >
          <ButtonText className={`text-base font-medium ${
            status !== AIAssistantStatus.READY || !inputText.trim()
              ? 'text-typography-500'
              : 'text-typography-0'
          }`}>发送</ButtonText>
        </Button>
      </HStack>
    </KeyboardAvoidingView>
  );
};


